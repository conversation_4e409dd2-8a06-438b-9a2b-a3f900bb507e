<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Professional Tic-Tac-Toe game with modern design and smooth animations">
    <meta name="author" content="Professional Web Developer">
    <title>Professional Tic-Tac-Toe</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <span class="title-icon">🎮</span>
                Professional Tic-Tac-Toe
            </h1>
            <p class="subtitle">Challenge yourself in this classic strategy game</p>
        </header>

        <main class="main-content">
            <!-- Game Stats -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="player-x-score">0</div>
                        <div class="stat-label">Player X Wins</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="draws">0</div>
                        <div class="stat-label">Draws</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="player-o-score">0</div>
                        <div class="stat-label">Player O Wins</div>
                    </div>
                </div>
            </section>

            <!-- Game Status -->
            <section class="game-status">
                <div class="current-player">
                    <span class="player-indicator" id="current-player-indicator">X</span>
                    <span class="player-text" id="current-player-text">Player X's Turn</span>
                </div>
                <div class="game-message" id="game-message"></div>
            </section>

            <!-- Game Board -->
            <section class="game-section">
                <div class="game-board" id="game-board">
                    <div class="cell" data-index="0"></div>
                    <div class="cell" data-index="1"></div>
                    <div class="cell" data-index="2"></div>
                    <div class="cell" data-index="3"></div>
                    <div class="cell" data-index="4"></div>
                    <div class="cell" data-index="5"></div>
                    <div class="cell" data-index="6"></div>
                    <div class="cell" data-index="7"></div>
                    <div class="cell" data-index="8"></div>
                </div>
            </section>

            <!-- Game Controls -->
            <section class="controls-section">
                <button class="btn btn-primary" id="reset-game">
                    New Game
                </button>
                <button class="btn btn-secondary" id="reset-stats">
                    Reset Stats
                </button>
            </section>
        </main>

        <footer class="footer">
            <p>Professional Tic-Tac-Toe. Built with modern web technologies.</p>
        </footer>
    </div>

    <!-- Victory Modal -->
    <div class="modal-overlay" id="victory-modal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title" id="modal-title">🎉 Game Over!</h2>
            </div>
            <div class="modal-body">
                <p class="modal-message" id="modal-message">Player X Wins!</p>
                <div class="modal-stats">
                    <div class="modal-stat">
                        <span class="modal-stat-label">Game Duration:</span>
                        <span class="modal-stat-value" id="game-duration">0:00</span>
                    </div>
                    <div class="modal-stat">
                        <span class="modal-stat-label">Moves Made:</span>
                        <span class="modal-stat-value" id="moves-count">0</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="play-again">
                    <span class="btn-icon">🎮</span>
                    Play Again
                </button>
                <button class="btn btn-secondary" id="close-modal">
                    Close
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
