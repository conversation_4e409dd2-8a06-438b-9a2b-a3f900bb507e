/**
 * Professional Tic-Tac-Toe Game
 * Features: Game state management, win detection, statistics tracking, animations
 */

class TicTacToeGame {
    constructor() {
        this.board = Array(9).fill('');
        this.currentPlayer = 'X';
        this.gameActive = true;
        this.gameStartTime = null;
        this.moveCount = 0;
        
        // Statistics
        this.stats = {
            playerXWins: parseInt(localStorage.getItem('playerXWins')) || 0,
            playerOWins: parseInt(localStorage.getItem('playerOWins')) || 0,
            draws: parseInt(localStorage.getItem('draws')) || 0
        };
        
        // Winning combinations
        this.winningCombinations = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
            [0, 4, 8], [2, 4, 6] // Diagonals
        ];
        
        this.initializeGame();
    }
    
    initializeGame() {
        this.bindEvents();
        this.updateDisplay();
        this.updateStats();
        this.startGameTimer();
        
        // Add pulse animation to current player indicator
        this.addPulseAnimation();
    }
    
    bindEvents() {
        // Game board events
        const cells = document.querySelectorAll('.cell');
        cells.forEach(cell => {
            cell.addEventListener('click', (e) => this.handleCellClick(e));
        });
        
        // Control buttons
        document.getElementById('reset-game').addEventListener('click', () => this.resetGame());
        document.getElementById('reset-stats').addEventListener('click', () => this.resetStats());
        
        // Modal events
        document.getElementById('play-again').addEventListener('click', () => this.playAgain());
        document.getElementById('close-modal').addEventListener('click', () => this.closeModal());
        
        // Close modal on overlay click
        document.getElementById('victory-modal').addEventListener('click', (e) => {
            if (e.target.id === 'victory-modal') {
                this.closeModal();
            }
        });
        
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
            if (e.key === 'Enter' && !this.gameActive) {
                this.playAgain();
            }
        });
    }
    
    handleCellClick(event) {
        const cell = event.target;
        const index = parseInt(cell.dataset.index);
        
        // Check if cell is already taken or game is not active
        if (this.board[index] !== '' || !this.gameActive) {
            return;
        }
        
        // Make the move
        this.makeMove(index, cell);
    }
    
    makeMove(index, cell) {
        // Update board state
        this.board[index] = this.currentPlayer;
        this.moveCount++;
        
        // Update cell display
        cell.textContent = this.currentPlayer;
        cell.classList.add(this.currentPlayer.toLowerCase());
        cell.classList.add('taken');
        
        // Check for win or draw
        if (this.checkWin()) {
            this.handleWin();
        } else if (this.checkDraw()) {
            this.handleDraw();
        } else {
            this.switchPlayer();
        }
    }
    
    checkWin() {
        return this.winningCombinations.some(combination => {
            const [a, b, c] = combination;
            if (this.board[a] && this.board[a] === this.board[b] && this.board[a] === this.board[c]) {
                this.winningCombination = combination;
                return true;
            }
            return false;
        });
    }
    
    checkDraw() {
        return this.board.every(cell => cell !== '');
    }
    
    handleWin() {
        this.gameActive = false;
        this.highlightWinningCells();
        this.updateStatsForWin();
        this.showVictoryModal(`Player ${this.currentPlayer} Wins!`, '🎉');
        this.removePulseAnimation();
        
        // Play win sound (if audio is enabled)
        this.playSound('win');
    }
    
    handleDraw() {
        this.gameActive = false;
        this.stats.draws++;
        this.saveStats();
        this.updateStats();
        this.showVictoryModal("It's a Draw!", '🤝');
        this.removePulseAnimation();
        
        // Play draw sound
        this.playSound('draw');
    }
    
    highlightWinningCells() {
        this.winningCombination.forEach(index => {
            const cell = document.querySelector(`[data-index="${index}"]`);
            cell.classList.add('winning');
        });
    }
    
    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 'X' ? 'O' : 'X';
        this.updateDisplay();
        this.updatePulseAnimation();
    }
    
    updateDisplay() {
        const indicator = document.getElementById('current-player-indicator');
        const text = document.getElementById('current-player-text');
        
        indicator.textContent = this.currentPlayer;
        text.textContent = `Player ${this.currentPlayer}'s Turn`;
        
        // Update indicator color based on player
        indicator.style.background = this.currentPlayer === 'X' 
            ? 'linear-gradient(135deg, var(--primary-color), var(--primary-dark))'
            : 'linear-gradient(135deg, var(--secondary-color), #6b46c1)';
    }
    
    updateStatsForWin() {
        if (this.currentPlayer === 'X') {
            this.stats.playerXWins++;
        } else {
            this.stats.playerOWins++;
        }
        this.saveStats();
        this.updateStats();
    }
    
    updateStats() {
        document.getElementById('player-x-score').textContent = this.stats.playerXWins;
        document.getElementById('player-o-score').textContent = this.stats.playerOWins;
        document.getElementById('draws').textContent = this.stats.draws;
    }
    
    saveStats() {
        localStorage.setItem('playerXWins', this.stats.playerXWins.toString());
        localStorage.setItem('playerOWins', this.stats.playerOWins.toString());
        localStorage.setItem('draws', this.stats.draws.toString());
    }
    
    showVictoryModal(message, icon) {
        const modal = document.getElementById('victory-modal');
        const title = document.getElementById('modal-title');
        const messageEl = document.getElementById('modal-message');
        const duration = document.getElementById('game-duration');
        const moves = document.getElementById('moves-count');
        
        title.textContent = `${icon} Game Over!`;
        messageEl.textContent = message;
        duration.textContent = this.getGameDuration();
        moves.textContent = this.moveCount;
        
        modal.classList.add('show');
    }
    
    closeModal() {
        const modal = document.getElementById('victory-modal');
        modal.classList.remove('show');
    }
    
    getGameDuration() {
        if (!this.gameStartTime) return '0:00';
        
        const duration = Math.floor((Date.now() - this.gameStartTime) / 1000);
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    startGameTimer() {
        this.gameStartTime = Date.now();
    }
    
    addPulseAnimation() {
        const indicator = document.getElementById('current-player-indicator');
        indicator.classList.add('pulse');
    }
    
    removePulseAnimation() {
        const indicator = document.getElementById('current-player-indicator');
        indicator.classList.remove('pulse');
    }
    
    updatePulseAnimation() {
        this.removePulseAnimation();
        // Small delay to ensure class is removed before adding it back
        setTimeout(() => this.addPulseAnimation(), 50);
    }
    
    playSound(type) {
        // Simple audio feedback using Web Audio API
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            if (type === 'win') {
                // Victory sound - ascending notes
                oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
                oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
                oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5
            } else if (type === 'draw') {
                // Draw sound - neutral tone
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4
            } else {
                // Move sound - simple click
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            }
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            // Audio not supported or blocked, fail silently
            console.log('Audio not available');
        }
    }
    
    resetGame() {
        this.board = Array(9).fill('');
        this.currentPlayer = 'X';
        this.gameActive = true;
        this.moveCount = 0;
        this.winningCombination = null;
        
        // Clear board display
        const cells = document.querySelectorAll('.cell');
        cells.forEach(cell => {
            cell.textContent = '';
            cell.classList.remove('x', 'o', 'taken', 'winning');
        });
        
        // Reset display
        this.updateDisplay();
        this.startGameTimer();
        this.addPulseAnimation();
        
        // Clear any messages
        document.getElementById('game-message').textContent = '';
    }
    
    playAgain() {
        this.closeModal();
        this.resetGame();
    }
    
    resetStats() {
        if (confirm('Are you sure you want to reset all statistics? This action cannot be undone.')) {
            this.stats = { playerXWins: 0, playerOWins: 0, draws: 0 };
            this.saveStats();
            this.updateStats();
            
            // Show confirmation message
            const messageEl = document.getElementById('game-message');
            messageEl.textContent = 'Statistics have been reset!';
            messageEl.style.color = 'var(--success-color)';
            
            setTimeout(() => {
                messageEl.textContent = '';
                messageEl.style.color = '';
            }, 3000);
        }
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TicTacToeGame();
});
