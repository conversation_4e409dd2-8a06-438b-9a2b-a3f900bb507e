/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Green Theme */
    --primary-color: #10b981;
    --primary-dark: #059669;
    --secondary-color: #34d399;
    --accent-color: #6ee7b7;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Dark Theme Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.title-icon {
    font-size: 2rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Stats Section */
.stats-section {
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Game Status */
.game-status {
    text-align: center;
}

.current-player {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.player-indicator {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    box-shadow: var(--shadow-sm);
}

.player-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.game-message {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-secondary);
    min-height: 1.5rem;
}

/* Game Board */
.game-section {
    display: flex;
    justify-content: center;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.cell {
    width: 100px;
    height: 100px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 700;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.cell:hover {
    background: var(--bg-tertiary);
}

.cell.taken {
    cursor: not-allowed;
}

.cell.taken:hover {
    background: var(--bg-secondary);
}

.cell.x {
    color: var(--primary-color);
}

.cell.o {
    color: var(--secondary-color);
}

.cell.winning {
    background: var(--primary-color);
    color: var(--bg-primary);
}

/* Controls Section */
.controls-section {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--bg-primary);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
}

.btn-icon {
    font-size: 1.1rem;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-md);
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.modal-body {
    padding: var(--spacing-xl);
    text-align: center;
}

.modal-message {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.modal-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    background: var(--bg-tertiary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
}

.modal-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.modal-stat-value {
    color: var(--primary-color);
    font-weight: 700;
}

.modal-footer {
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-xl);
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}



/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-md);
    }

    .title {
        font-size: 2rem;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .cell {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .controls-section {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }

    .modal {
        margin: var(--spacing-md);
    }

    .modal-footer {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .game-board {
        padding: var(--spacing-md);
    }

    .cell {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }

    .current-player {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .player-indicator {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.25rem;
    }
}
